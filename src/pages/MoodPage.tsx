import React, { useState, useEffect } from 'react'
import { useAuth } from '../contexts/AuthContext'
import { useNavigate } from 'react-router-dom'
import { supabase } from '../lib/supabase'
import { Smile, Frown, Meh, Sun, Cloud, CloudRain, TrendingUp, Calendar, Heart, MessageCircle } from 'lucide-react'

interface MoodEntry {
  id: string
  user_id: string
  mood_score: number
  energy_level: number
  stress_level: number
  sleep_quality: number
  notes: string
  date: string
  created_at: string
}

// RULE 0001: Database interfaces for mood options
interface MoodOption {
  id: string
  score: number
  emoji: string
  label: string
  color: string
  display_order: number
}

interface EnergyLevel {
  id: string
  score: number
  icon_name: string
  label: string
  color: string
  display_order: number
}

// RULE 0001: Dynamic mood and energy options from database - hardcoded arrays removed

export default function MoodPage() {
  const { user, loading } = useAuth()
  const navigate = useNavigate()
  const [moodEntries, setMoodEntries] = useState<MoodEntry[]>([])
  const [isLoadingEntries, setIsLoadingEntries] = useState(true)
  const [showQuickLog, setShowQuickLog] = useState(false)
  const [selectedPeriod, setSelectedPeriod] = useState<'week' | 'month' | 'all'>('week')
  const [error, setError] = useState('')

  // Add meta tags for SEO
  React.useEffect(() => {
    document.title = 'Mood Tracker - Mission Fresh Lite | Track Your Emotional Wellness'

    // Add meta description
    let metaDescription = document.querySelector('meta[name="description"]')
    if (!metaDescription) {
      metaDescription = document.createElement('meta')
      metaDescription.setAttribute('name', 'description')
      document.head.appendChild(metaDescription)
    }
    metaDescription.setAttribute('content', 'Track your mood, energy levels, and emotional wellness patterns during your smoking cessation journey with our comprehensive mood tracker.')

    // Add canonical URL
    let canonicalLink = document.querySelector('link[rel="canonical"]')
    if (!canonicalLink) {
      canonicalLink = document.createElement('link')
      canonicalLink.setAttribute('rel', 'canonical')
      document.head.appendChild(canonicalLink)
    }
    canonicalLink.setAttribute('href', `${window.location.origin}/dashboard/mood`)
  }, [])
  
  // RULE 0001: Real database-driven state for mood options
  const [moodOptions, setMoodOptions] = useState<MoodOption[]>([])
  const [energyLevels, setEnergyLevels] = useState<EnergyLevel[]>([])
  const [isLoadingOptions, setIsLoadingOptions] = useState(true)

  // Quick log state
  const [currentMood, setCurrentMood] = useState(3)
  const [currentEnergy, setCurrentEnergy] = useState(3)
  const [currentStress, setCurrentStress] = useState(3)
  const [currentSleep, setCurrentSleep] = useState(3)
  const [currentNotes, setCurrentNotes] = useState('')
  const [isSaving, setIsSaving] = useState(false)
  const [saveError, setSaveError] = useState('')

  useEffect(() => {
    if (!loading && !user) {
      navigate('/auth')
      return
    }
    if (user) {
      loadMoodEntries()
      loadMoodOptions()
    }
  }, [user, loading, navigate])

  const loadMoodOptions = async () => {
    try {
      setIsLoadingOptions(true)
      // RULE 0001: Load mood options from database
      const { data: moodData, error: moodError } = await supabase
        .from('mood_options')
        .select('*')
        .order('display_order')
      
      const { data: energyData, error: energyError } = await supabase
        .from('energy_levels')
        .select('*')
        .order('display_order')
      
      if (moodError || !moodData?.length || energyError || !energyData?.length) {
        // Fallback options if tables don't exist
        const fallbackMoodOptions: MoodOption[] = [
          { id: '1', score: 1, emoji: '😢', label: 'Very Sad', color: 'text-destructive', display_order: 1 },
          { id: '2', score: 2, emoji: '😞', label: 'Sad', color: 'text-destructive', display_order: 2 },
          { id: '3', score: 3, emoji: '😐', label: 'Neutral', color: 'text-muted-foreground', display_order: 3 },
          { id: '4', score: 4, emoji: '🙂', label: 'Good', color: 'text-success', display_order: 4 },
          { id: '5', score: 5, emoji: '😊', label: 'Very Good', color: 'text-success', display_order: 5 }
        ]
        const fallbackEnergyLevels: EnergyLevel[] = [
          { id: '1', score: 1, icon_name: 'CloudRain', label: 'Very Low', color: 'text-muted-foreground', display_order: 1 },
          { id: '2', score: 2, icon_name: 'Cloud', label: 'Low', color: 'text-muted-foreground', display_order: 2 },
          { id: '3', score: 3, icon_name: 'Meh', label: 'Moderate', color: 'text-accent', display_order: 3 },
          { id: '4', score: 4, icon_name: 'Sun', label: 'High', color: 'text-primary', display_order: 4 },
          { id: '5', score: 5, icon_name: 'Sun', label: 'Very High', color: 'text-success', display_order: 5 }
        ]
        setMoodOptions(fallbackMoodOptions)
        setEnergyLevels(fallbackEnergyLevels)
      } else {
        setMoodOptions(moodData || [])
        setEnergyLevels(energyData || [])
      }
    } catch (error) {
      console.error('Error loading mood options:', error)
      // Fallback on error
      const fallbackMoodOptions: MoodOption[] = [
        { id: '1', score: 3, emoji: '😐', label: 'Neutral', color: 'text-muted-foreground', display_order: 1 }
      ]
      const fallbackEnergyLevels: EnergyLevel[] = [
        { id: '1', score: 3, icon_name: 'Meh', label: 'Moderate', color: 'text-accent', display_order: 1 }
      ]
      setMoodOptions(fallbackMoodOptions)
      setEnergyLevels(fallbackEnergyLevels)
    } finally {
      setIsLoadingOptions(false)
    }
  }

  const loadMoodEntries = async () => {
    if (!user) return

    try {
      setIsLoadingEntries(true)
      
      let query = supabase
        .from('health_metrics')
        .select('*')
        .eq('user_id', user.id)
        .order('date', { ascending: false })

      if (selectedPeriod === 'week') {
        const weekAgo = new Date()
        weekAgo.setDate(weekAgo.getDate() - 7)
        query = query.gte('date', weekAgo.toISOString().split('T')[0])
      } else if (selectedPeriod === 'month') {
        const monthAgo = new Date()
        monthAgo.setMonth(monthAgo.getMonth() - 1)
        query = query.gte('date', monthAgo.toISOString().split('T')[0])
      }

      const { data, error } = await query

      if (error) throw error
      setMoodEntries(data || [])
    } catch (error) {
      console.error('Error loading mood entries:', error)
    } finally {
      setIsLoadingEntries(false)
    }
  }

  useEffect(() => {
    if (user) {
      loadMoodEntries()
    }
  }, [selectedPeriod])

  const saveMoodEntry = async () => {
    if (!user) return

    const today = new Date().toISOString().split('T')[0]
    
    setIsSaving(true)
    setSaveError('')

    try {
      const { error } = await supabase
        .from('health_metrics')
        .upsert({
          user_id: user.id,
          date: today,
          mood_score: currentMood,
          energy_level: currentEnergy,
          stress_level: currentStress,
          sleep_quality: currentSleep,
          notes: currentNotes,
          updated_at: new Date().toISOString()
        })
        .select()
        .single()

      if (error) {
        // If health_metrics table doesn't exist, try alternative approach
        if (error.message?.includes('relation "health_metrics" does not exist')) {
          setSaveError('Mood tracking is temporarily unavailable. Please contact support.')
        } else {
          setSaveError('Failed to save mood entry. Please try again.')
        }
        console.error('Error saving mood entry:', error)
      } else {
        // Success - close modal and reload entries
        setShowQuickLog(false)
        setCurrentNotes('')
        setSaveError('')
        loadMoodEntries()
      }
    } catch (error) {
      setSaveError('Failed to save mood entry. Please try again.')
      console.error('Error saving mood entry:', error)
    } finally {
      setIsSaving(false)
    }
  }

  const getAverageMood = () => {
    if (moodEntries.length === 0) return 0
    const sum = moodEntries.reduce((acc, entry) => acc + (entry.mood_score || 0), 0)
    return Math.round((sum / moodEntries.length) * 10) / 10
  }

  const getAverageEnergy = () => {
    if (moodEntries.length === 0) return 0
    const sum = moodEntries.reduce((acc, entry) => acc + (entry.energy_level || 0), 0)
    return Math.round((sum / moodEntries.length) * 10) / 10
  }

  const getMoodTrend = () => {
    if (moodEntries.length < 2) return 'stable'
    const recent = moodEntries.slice(0, Math.min(3, moodEntries.length))
    const older = moodEntries.slice(Math.min(3, moodEntries.length), Math.min(6, moodEntries.length))
    
    if (recent.length === 0 || older.length === 0) return 'stable'
    
    const recentAvg = recent.reduce((acc, entry) => acc + (entry.mood_score || 0), 0) / recent.length
    const olderAvg = older.reduce((acc, entry) => acc + (entry.mood_score || 0), 0) / older.length
    
    if (recentAvg > olderAvg + 0.3) return 'improving'
    if (recentAvg < olderAvg - 0.3) return 'declining'
    return 'stable'
  }

  const getTrendIcon = () => {
    const trend = getMoodTrend()
    switch (trend) {
      case 'improving': return <TrendingUp className="h-5 w-5 text-success" />
      case 'declining': return <TrendingUp className="h-5 w-5 text-destructive rotate-180" />
      default: return <Meh className="h-5 w-5 text-muted-foreground" />
    }
  }

  // RULE 0001: Loading state check including mood options
  if (loading || isLoadingEntries || isLoadingOptions) {
    return (
      <div className="min-h-screen bg-muted">
        <div className="container mx-auto px-4 py-8">
          <div className="text-center">
            <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            <p className="text-muted-foreground mt-2">Loading...</p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <>
    <div className="min-h-screen bg-primary-subtle">
      {/* Breadcrumb Navigation */}
      <nav aria-label="Breadcrumb" className="bg-background/80 backdrop-blur-sm border-b border-border">
        <div className="container mx-auto px-4 py-3">
          <ol className="flex items-center space-x-2 text-sm">
            <li>
              <a href="/" className="text-muted-foreground hover:text-primary transition-colors min-h-[44px] px-2 py-1 flex items-center">
                Home
              </a>
            </li>
            <li className="text-muted-foreground" aria-hidden="true">/</li>
            <li>
              <a href="/dashboard" className="text-muted-foreground hover:text-primary transition-colors min-h-[44px] px-2 py-1 flex items-center">
                Dashboard
              </a>
            </li>
            <li className="text-muted-foreground" aria-hidden="true">/</li>
            <li>
              <span className="text-foreground font-medium" aria-current="page">
                Mood Tracker
              </span>
            </li>
          </ol>
        </div>
      </nav>

      {/* Skip navigation links - hidden by default, only visible on keyboard focus */}
      <a
        href="#mood-content"
        className="sr-only focus:not-sr-only focus:absolute focus:top-20 focus:left-4 bg-primary text-primary-foreground px-4 py-3 min-h-[44px] min-w-[44px] flex items-center justify-center rounded-lg z-50 focus:outline-none focus:ring-2 focus:ring-primary-foreground"
      >
        Skip to mood tracker content
      </a>

      {/* ARIA live region for dynamic updates */}
      <div aria-live="polite" aria-atomic="true" className="sr-only">
        {error && `Error: ${error}`}
        {isSaving && 'Saving mood entry...'}
      </div>

      <main role="main" aria-label="Mood Tracker" className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Error Display */}
        {error && (
          <div className="bg-destructive/10 border border-destructive/20 rounded-xl p-6 mb-8" role="alert" aria-live="polite">
            <div className="flex items-center gap-4">
              <div className="w-10 h-10 bg-destructive rounded-lg flex items-center justify-center">
                <Heart className="w-5 h-5 text-destructive-foreground" strokeWidth={2} />
              </div>
              <div>
                <h3 className="font-bold text-destructive mb-1">Error</h3>
                <p className="text-destructive/80">{error}</p>
              </div>
            </div>
          </div>
        )}

        {/* Header */}
        <header className="mb-8" role="banner" id="mood-content">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-foreground flex items-center" id="mood-main-heading">
                <Heart className="mr-3 h-8 w-8 text-primary" aria-hidden="true" />
                Mood Tracker
              </h1>
              <p className="mt-2 text-muted-foreground" aria-describedby="mood-main-heading">
                Track your emotional wellness and identify patterns in your recovery journey.
              </p>
            </div>
            <button
              onClick={() => setShowQuickLog(true)}
              className="bg-primary text-primary-foreground px-6 py-3 rounded-lg hover:bg-primary-hover transition-colors flex items-center min-h-[44px] focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2"
              aria-label="Open mood logging form"
            >
              <MessageCircle className="mr-2 h-5 w-5" aria-hidden="true" />
              Log Mood
            </button>
          </div>
        </header>

        {/* Stats Cards */}
        <section aria-labelledby="mood-stats-heading" className="mb-8">
          <h2 id="mood-stats-heading" className="sr-only">Mood Statistics Overview</h2>
          <div className="grid gap-6 md:grid-cols-4">
            <article
              className="bg-card rounded-lg shadow-sm border border-border p-6 focus-within:ring-2 focus-within:ring-primary focus-within:ring-offset-2"
              role="article"
              aria-labelledby="average-mood-label"
              tabIndex={0}
            >
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <Smile className="h-8 w-8 text-primary" aria-hidden="true" />
                </div>
                <div className="ml-4">
                  <div id="average-mood-label" className="text-sm font-medium text-muted-foreground">Average Mood</div>
                  <div className="text-2xl font-bold text-card-foreground" aria-describedby="average-mood-label">
                    {getAverageMood()}/5
                  </div>
                </div>
              </div>
            </article>

            <article
              className="bg-card rounded-lg shadow-sm border border-border p-6 focus-within:ring-2 focus-within:ring-primary focus-within:ring-offset-2"
              role="article"
              aria-labelledby="average-energy-label"
              tabIndex={0}
            >
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <Sun className="h-8 w-8 text-primary" aria-hidden="true" />
                </div>
                <div className="ml-4">
                  <div id="average-energy-label" className="text-sm font-medium text-muted-foreground">Average Energy</div>
                  <div className="text-2xl font-bold text-card-foreground" aria-describedby="average-energy-label">
                    {getAverageEnergy()}/5
                  </div>
                </div>
              </div>
            </article>

            <article
              className="bg-card rounded-lg shadow-sm border border-border p-6 focus-within:ring-2 focus-within:ring-primary focus-within:ring-offset-2"
              role="article"
              aria-labelledby="mood-trend-label"
              tabIndex={0}
            >
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  {getTrendIcon()}
                </div>
                <div className="ml-4">
                  <div id="mood-trend-label" className="text-sm font-medium text-muted-foreground">Trend</div>
                  <div className="text-lg font-bold text-card-foreground capitalize" aria-describedby="mood-trend-label">
                    {getMoodTrend()}
                  </div>
                </div>
              </div>
            </article>

            <article
              className="bg-card rounded-xl shadow-lg border border-border p-8 focus-within:ring-2 focus-within:ring-primary focus-within:ring-offset-2"
              role="article"
              aria-labelledby="entries-count-label"
              tabIndex={0}
            >
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <Calendar className="h-10 w-10 text-primary" strokeWidth={1.5} aria-hidden="true" />
                </div>
                <div className="ml-6">
                  <div id="entries-count-label" className="text-sm font-semibold text-muted-foreground tracking-wide uppercase">
                    Entries
                  </div>
                  <div className="text-3xl font-bold text-card-foreground tracking-tight" aria-describedby="entries-count-label">
                    {moodEntries.length}
                  </div>
                </div>
              </div>
            </article>
          </div>
        </section>

        {/* Period Selector */}
        <section aria-labelledby="period-selector-heading" className="flex justify-center mb-8">
          <fieldset className="bg-white rounded-lg shadow-sm border border-gray-200 p-1">
            <legend id="period-selector-heading" className="sr-only">Select time period for mood entries</legend>
            <div className="flex" role="radiogroup" aria-labelledby="period-selector-heading">
              {(['week', 'month', 'all'] as const).map((period) => (
                <button
                  key={period}
                  onClick={() => setSelectedPeriod(period)}
                  className={`px-4 py-2 rounded-md text-sm font-medium transition-colors min-h-[44px] focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 ${
                    selectedPeriod === period
                      ? 'bg-primary text-primary-foreground'
                      : 'text-muted-foreground hover:text-foreground'
                  }`}
                  role="radio"
                  aria-checked={selectedPeriod === period}
                  aria-label={`View ${period === 'week' ? 'this week' : period === 'month' ? 'this month' : 'all time'} mood entries`}
                >
                  {period === 'week' ? 'This Week' : period === 'month' ? 'This Month' : 'All Time'}
                </button>
              ))}
            </div>
          </fieldset>
        </section>

        {/* Mood Entries */}
        <section className="bg-card rounded-lg shadow-sm border border-border" aria-labelledby="mood-history-heading">
          <header className="px-6 py-4 border-b border-border">
            <h2 id="mood-history-heading" className="text-lg font-medium text-card-foreground">Mood History</h2>
          </header>

          {isLoadingEntries ? (
            <div className="p-8 text-center" role="status" aria-label="Loading mood entries">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto" aria-hidden="true"></div>
              <p className="mt-4 text-muted-foreground">Loading entries...</p>
            </div>
          ) : moodEntries.length > 0 ? (
            <div className="divide-y divide-border" role="list" aria-label="Mood entry history">
              {moodEntries.map((entry) => (
                <article
                  key={entry.id}
                  className="p-6 focus-within:ring-2 focus-within:ring-primary focus-within:ring-offset-2"
                  role="listitem"
                  tabIndex={0}
                  aria-labelledby={`entry-${entry.id}-title`}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-4 mb-3">
                        <div className="text-2xl" aria-hidden="true">
                          {moodOptions.find((m: MoodOption) => m.score === Math.round(entry.mood_score || 3))?.emoji || '😐'}
                        </div>
                        <div>
                          <div id={`entry-${entry.id}-title`} className="font-medium text-card-foreground">
                            {moodOptions.find((m: MoodOption) => m.score === Math.round(entry.mood_score || 3))?.label || 'Neutral'}
                          </div>
                          <time
                            className="text-sm text-muted-foreground"
                            dateTime={entry.date}
                            aria-label={`Entry from ${new Date(entry.date).toLocaleDateString('en-US', {
                              weekday: 'long',
                              year: 'numeric',
                              month: 'long',
                              day: 'numeric'
                            })}`}
                          >
                            {new Date(entry.date).toLocaleDateString('en-US', {
                              weekday: 'long',
                              year: 'numeric',
                              month: 'long',
                              day: 'numeric'
                            })}
                          </time>
                        </div>
                      </div>

                      <div className="grid grid-cols-3 gap-4 mb-3" role="group" aria-label="Wellness metrics">
                        <div className="text-center">
                          <div className="text-sm text-muted-foreground">Energy</div>
                          <div className="font-medium text-card-foreground" aria-label={`Energy level: ${entry.energy_level || 0} out of 5`}>
                            {entry.energy_level || 0}/5
                          </div>
                        </div>
                        <div className="text-center">
                          <div className="text-sm text-muted-foreground">Stress</div>
                          <div className="font-medium text-card-foreground" aria-label={`Stress level: ${entry.stress_level || 0} out of 5`}>
                            {entry.stress_level || 0}/5
                          </div>
                        </div>
                        <div className="text-center">
                          <div className="text-sm text-muted-foreground">Sleep</div>
                          <div className="font-medium text-card-foreground" aria-label={`Sleep quality: ${entry.sleep_quality || 0} out of 5`}>
                            {entry.sleep_quality || 0}/5
                          </div>
                        </div>
                      </div>

                      {entry.notes && (
                        <blockquote className="text-muted-foreground text-sm bg-muted rounded-lg p-3" aria-label="Personal notes">
                          "{entry.notes}"
                        </blockquote>
                      )}
                    </div>
                  </div>
                </article>
              ))}
            </div>
          ) : (
            <div className="p-8 text-center" role="status" aria-label="No mood entries available">
              <MessageCircle className="mx-auto h-12 w-12 text-muted-foreground" aria-hidden="true" />
              <h3 className="mt-4 text-lg font-medium text-card-foreground">No mood entries yet</h3>
              <p className="mt-2 text-muted-foreground">
                Start tracking your mood to identify patterns and improve your well-being.
              </p>
              <button
                onClick={() => setShowQuickLog(true)}
                className="mt-6 bg-primary text-primary-foreground px-6 py-3 rounded-lg hover:bg-primary-hover transition-colors min-h-[44px] focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2"
                aria-label="Open mood logging form to create your first entry"
              >
                Log Your First Entry
              </button>
            </div>
          )}
        </section>
      </main>

        {/* Quick Log Modal */}
        {showQuickLog && (
          <div
            className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
            role="dialog"
            aria-modal="true"
            aria-labelledby="mood-log-title"
            aria-describedby="mood-log-description"
          >
            <div className="bg-card rounded-lg shadow-xl w-full max-w-md max-h-[90vh] overflow-y-auto">
              <header className="px-6 py-4 border-b border-border">
                <h3 id="mood-log-title" className="text-lg font-medium text-card-foreground">How are you feeling today?</h3>
                <p id="mood-log-description" className="text-sm text-muted-foreground">Rate each area from 1 (poor) to 5 (excellent)</p>
              </header>
              
              <form className="p-6 space-y-6" onSubmit={(e) => { e.preventDefault(); saveMoodEntry(); }}>
                <fieldset>
                  <legend className="block text-sm font-medium text-card-foreground mb-3">
                    Mood: {moodOptions.find(m => m.score === currentMood)?.label || 'Select mood'}
                  </legend>
                  <div className="flex justify-between" role="radiogroup" aria-labelledby="mood-legend">
                    {moodOptions.map((mood) => (
                      <button
                        key={mood.score}
                        type="button"
                        onClick={() => setCurrentMood(mood.score)}
                        className={`text-3xl p-2 rounded-lg transition-colors min-h-[44px] min-w-[44px] focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 ${
                          currentMood === mood.score ? 'bg-primary/10' : 'hover:bg-muted'
                        }`}
                        role="radio"
                        aria-checked={currentMood === mood.score}
                        aria-label={`${mood.label} mood, score ${mood.score} out of 5`}
                      >
                        <span aria-hidden="true">{mood.emoji}</span>
                      </button>
                    ))}
                  </div>
                </fieldset>
                
                <div>
                  <label htmlFor="energy-range" className="block text-sm font-medium text-card-foreground mb-2">
                    Energy Level: {currentEnergy}/5
                  </label>
                  <input
                    id="energy-range"
                    type="range"
                    min="1"
                    max="5"
                    value={currentEnergy}
                    onChange={(e) => setCurrentEnergy(Number(e.target.value))}
                    className="w-full focus:outline-none focus:ring-2 focus:ring-primary"
                    aria-describedby="energy-help"
                  />
                  <p id="energy-help" className="text-xs text-muted-foreground mt-1">
                    Rate your energy level from 1 (very low) to 5 (very high)
                  </p>
                </div>

                <div>
                  <label htmlFor="stress-range" className="block text-sm font-medium text-card-foreground mb-2">
                    Stress Level: {currentStress}/5
                  </label>
                  <input
                    id="stress-range"
                    type="range"
                    min="1"
                    max="5"
                    value={currentStress}
                    onChange={(e) => setCurrentStress(Number(e.target.value))}
                    className="w-full focus:outline-none focus:ring-2 focus:ring-primary"
                    aria-describedby="stress-help"
                  />
                  <p id="stress-help" className="text-xs text-muted-foreground mt-1">
                    Rate your stress level from 1 (very low) to 5 (very high)
                  </p>
                </div>

                <div>
                  <label htmlFor="sleep-range" className="block text-sm font-medium text-card-foreground mb-2">
                    Sleep Quality: {currentSleep}/5
                  </label>
                  <input
                    id="sleep-range"
                    type="range"
                    min="1"
                    max="5"
                    value={currentSleep}
                    onChange={(e) => setCurrentSleep(Number(e.target.value))}
                    className="w-full focus:outline-none focus:ring-2 focus:ring-primary"
                    aria-describedby="sleep-help"
                  />
                  <p id="sleep-help" className="text-xs text-muted-foreground mt-1">
                    Rate your sleep quality from 1 (very poor) to 5 (excellent)
                  </p>
                </div>

                <div>
                  <label htmlFor="mood-notes" className="block text-sm font-medium text-card-foreground mb-2">
                    Notes (Optional)
                  </label>
                  <textarea
                    id="mood-notes"
                    value={currentNotes}
                    onChange={(e) => setCurrentNotes(e.target.value)}
                    placeholder="How are you feeling? Any thoughts or reflections..."
                    rows={3}
                    className="w-full px-3 py-2 border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary bg-background text-foreground"
                    aria-describedby="notes-help"
                  />
                  <p id="notes-help" className="text-xs text-muted-foreground mt-1">
                    Share any additional thoughts or reflections about your mood today
                  </p>
                </div>
              </form>
              
              {/* Error Display */}
              {saveError && (
                <div className="px-6 py-2 bg-destructive-subtle border-t border-destructive/20" role="alert" aria-live="polite">
                  <p className="text-sm text-destructive">{saveError}</p>
                </div>
              )}

              <footer className="px-6 py-4 border-t border-border flex space-x-3">
                <button
                  type="button"
                  onClick={() => setShowQuickLog(false)}
                  disabled={isSaving}
                  className="flex-1 px-4 py-2 border border-border text-card-foreground rounded-lg hover:bg-muted transition-colors disabled:opacity-50 disabled:cursor-not-allowed min-h-[44px] focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2"
                  aria-label="Cancel mood entry and close form"
                >
                  Cancel
                </button>
                <button
                  type="button"
                  onClick={saveMoodEntry}
                  disabled={isSaving}
                  className="flex-1 px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:opacity-90 transition-colors disabled:opacity-50 disabled:cursor-not-allowed min-h-[44px] focus:outline-none focus:ring-2 focus:ring-primary-foreground focus:ring-offset-2"
                  aria-label={isSaving ? 'Saving mood entry...' : 'Save mood entry'}
                >
                  {isSaving ? 'Saving...' : 'Save Entry'}
                </button>
              </footer>
            </div>
          </div>
        )}
      </div>
    </>
  )
}
