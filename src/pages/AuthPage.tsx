import { useState, useEffect } from 'react'
import { Mail, Lock, Eye, EyeOff } from 'lucide-react'
import { useAuth } from '../contexts/AuthContext'
import { useNavigate, useSearchParams } from 'react-router-dom'
import Logo from '../components/Logo'

export default function AuthPage() {
  const { signIn, signUp, resetPassword, user } = useAuth()
  const navigate = useNavigate()
  const [searchParams] = useSearchParams()
  const [isSignUp, setIsSignUp] = useState(false)
  const [isPasswordReset, setIsPasswordReset] = useState(false)
  const [showPassword, setShowPassword] = useState(false)
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [confirmPassword, setConfirmPassword] = useState('')
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const [success, setSuccess] = useState('')
  const [emailError, setEmailError] = useState('')
  const [passwordError, setPasswordError] = useState('')
  const [confirmPasswordError, setConfirmPasswordError] = useState('')
  const [passwordStrength, setPasswordStrength] = useState(0)

  // Check URL parameters to set initial mode
  useEffect(() => {
    const mode = searchParams.get('mode')
    const action = searchParams.get('action')
    
    if (mode === 'signin' || action === 'signin') {
      setIsSignUp(false)
    } else if (mode === 'signup' || action === 'signup') {
      setIsSignUp(true)
    }
  }, [searchParams])

  // Redirect if user is already authenticated
  if (user) {
    navigate('/dashboard')
    return null
  }

  const validateEmail = (email: string) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(email)
  }

  const validatePassword = (password: string) => {
    return password.length >= 8
  }

  const calculatePasswordStrength = (password: string) => {
    let strength = 0
    if (password.length >= 8) strength += 1
    if (/[A-Z]/.test(password)) strength += 1
    if (/[a-z]/.test(password)) strength += 1
    if (/[0-9]/.test(password)) strength += 1
    if (/[^A-Za-z0-9]/.test(password)) strength += 1
    return strength
  }

  const getPasswordStrengthText = (strength: number) => {
    switch (strength) {
      case 0:
      case 1: return 'Weak'
      case 2:
      case 3: return 'Medium'
      case 4:
      case 5: return 'Strong'
      default: return 'Weak'
    }
  }

  const getPasswordStrengthColor = (strength: number) => {
    switch (strength) {
      case 0:
      case 1: return 'text-destructive'
      case 2:
      case 3: return 'text-warning'
      case 4:
      case 5: return 'text-success'
      default: return 'text-destructive'
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError('')
    setSuccess('')
    setEmailError('')
    setPasswordError('')

    // Validation
    if (!validateEmail(email)) {
      setEmailError('Please enter a valid email address')
      setLoading(false)
      return
    }

    if (!validatePassword(password)) {
      setPasswordError('Password must be at least 8 characters long')
      setLoading(false)
      return
    }

    if (isSignUp && password !== confirmPassword) {
      setPasswordError('Passwords do not match')
      setLoading(false)
      return
    }

    try {
      const { error: authError } = isSignUp
        ? await signUp(email, password)
        : await signIn(email, password)

      if (authError) {
        setError(authError.message)
      } else {
        setSuccess(isSignUp ? 'Account created successfully!' : 'Welcome back!')
        setTimeout(() => navigate('/dashboard'), 1500)
      }
    } catch (err) {
      setError('An unexpected error occurred. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  const handlePasswordReset = async () => {
    if (!email) {
      setEmailError('Please enter your email address first')
      return
    }

    if (!validateEmail(email)) {
      setEmailError('Please enter a valid email address')
      return
    }

    setLoading(true)
    setError('')
    setSuccess('')
    setEmailError('')

    try {
      const { error } = await resetPassword(email)

      if (error) {
        setError(error.message)
      } else {
        setSuccess('Password reset email sent! Check your inbox for instructions.')
        setIsPasswordReset(true)
      }
    } catch (err) {
      setError('Failed to send password reset email. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background to-muted relative overflow-hidden">
      <main role="main" aria-label="Mission Fresh Authentication">

      {/* Breadcrumb Navigation */}
      <nav aria-label="Breadcrumb" className="absolute top-0 left-0 right-0 z-40 bg-background/80 backdrop-blur-sm border-b border-border">
        <div className="container mx-auto px-4 py-3">
          <ol className="flex items-center space-x-2 text-sm">
            <li>
              <a href="/" className="text-muted-foreground hover:text-primary transition-colors min-h-[44px] px-2 py-1 flex items-center">
                Home
              </a>
            </li>
            <li className="text-muted-foreground" aria-hidden="true">/</li>
            <li>
              <span className="text-foreground font-medium" aria-current="page">
                {isSignUp ? 'Sign Up' : 'Sign In'}
              </span>
            </li>
          </ol>
        </div>
      </nav>

      {/* Skip navigation links - hidden by default, only visible on keyboard focus */}
      <a
        href="#auth-form"
        className="sr-only focus:not-sr-only focus:absolute focus:top-20 focus:left-4 bg-primary text-primary-foreground px-4 py-3 min-h-[44px] min-w-[44px] flex items-center justify-center rounded-lg z-50 focus:outline-none focus:ring-2 focus:ring-primary-foreground"
      >
        Skip to authentication form
      </a>

      {/* ARIA live region for form feedback */}
      <div aria-live="polite" aria-atomic="true" className="sr-only">
        {error && `Error: ${error}`}
        {success && `Success: ${success}`}
        {loading && 'Processing authentication...'}
      </div>

      {/* Background decoration */}
      <div className="absolute inset-0 bg-grid-pattern opacity-5"></div>
      <div className="absolute top-0 left-0 w-72 h-72 bg-primary/10 rounded-full blur-3xl"></div>
      <div className="absolute bottom-0 right-0 w-96 h-96 bg-accent/10 rounded-full blur-3xl"></div>

      <div className="min-h-screen flex items-center justify-center p-6 pt-24 relative">

        <div className="max-w-lg w-full relative z-10">
          <main className="auth-form rounded-2xl shadow-2xl p-12 backdrop-blur-sm">
          {/* Logo and Header */}
          <header className="text-center mb-12">
            <div className="flex justify-center mb-8">
              <Logo size="xl" showText={true} linkTo="/" />
            </div>
            <h1 className="text-2xl font-bold text-foreground mb-2">
              {isSignUp ? 'Create Your Account' : 'Welcome Back'}
            </h1>
            <p className="text-xl text-muted-foreground leading-relaxed">
              {isSignUp ? 'Start your smoke-free journey today' : 'Continue your wellness journey'}
            </p>
          </header>

          {/* Auth Form */}
          <form id="auth-form" onSubmit={handleSubmit} className="space-y-8" role="form" aria-label={isSignUp ? "Create account form" : "Sign in form"}>
            {/* Email Field */}
            <div>
              <label htmlFor="email" className="block text-base font-semibold text-card-foreground mb-4">
                Email
              </label>
              <div className="relative">
                <Mail className="absolute left-4 top-1/2 transform -translate-y-1/2 w-6 h-6 text-muted-foreground" strokeWidth={1.5} />
                <input
                  type="email"
                  id="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  className="auth-input pl-12 pr-4 py-4 min-h-[44px] rounded-lg focus:ring-2 focus:ring-primary text-lg"
                  placeholder="Enter your email"
                  required
                  aria-describedby={emailError ? "email-error" : undefined}
                  aria-invalid={emailError ? "true" : "false"}
                />
              </div>
              {emailError && (
                <div id="email-error" className="mt-2 text-sm text-destructive" role="alert" aria-live="polite">
                  {emailError}
                </div>
              )}
            </div>

            {/* Password Field */}
            <div>
              <label htmlFor="password" className="block text-base font-semibold text-card-foreground mb-4">
                Password
              </label>
              <div className="relative">
                <Lock className="absolute left-4 top-1/2 transform -translate-y-1/2 w-6 h-6 text-muted-foreground" strokeWidth={1.5} />
                <input
                  type={showPassword ? 'text' : 'password'}
                  id="password"
                  value={password}
                  onChange={(e) => {
                    setPassword(e.target.value)
                    setPasswordStrength(calculatePasswordStrength(e.target.value))
                  }}
                  className="auth-input pl-12 pr-12 py-4 min-h-[44px] rounded-lg focus:ring-2 focus:ring-primary text-lg"
                  placeholder="Enter your password"
                  required
                  aria-describedby={passwordError ? "password-error" : undefined}
                  aria-invalid={passwordError ? "true" : "false"}
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute right-4 top-1/2 transform -translate-y-1/2 text-muted-foreground hover:text-primary transition-colors duration-300 min-h-[44px] min-w-[44px] flex items-center justify-center"
                  aria-label={showPassword ? "Hide password" : "Show password"}
                >
                  {showPassword ? <EyeOff className="w-6 h-6" strokeWidth={1.5} aria-hidden="true" /> : <Eye className="w-6 h-6" strokeWidth={1.5} aria-hidden="true" />}
                </button>
              </div>
              {passwordError && (
                <div id="password-error" className="mt-2 text-sm text-destructive" role="alert" aria-live="polite">
                  {passwordError}
                </div>
              )}
              {isSignUp && password && (
                <div className="mt-3">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm text-muted-foreground">Password strength:</span>
                    <span className={`text-sm font-medium ${getPasswordStrengthColor(passwordStrength)}`}>
                      {getPasswordStrengthText(passwordStrength)}
                    </span>
                  </div>
                  <div className="w-full bg-muted rounded-full h-2">
                    <div
                      className={`h-2 rounded-full transition-all duration-300 ${
                        passwordStrength <= 1 ? 'bg-destructive' :
                        passwordStrength <= 3 ? 'bg-warning' : 'bg-success'
                      }`}
                      style={{ width: `${(passwordStrength / 5) * 100}%` }}
                    />
                  </div>
                </div>
              )}
            </div>

            {/* Confirm Password Field for Sign Up */}
            {isSignUp && (
              <div>
                <label htmlFor="confirmPassword" className="block text-base font-semibold text-card-foreground mb-4">
                  Confirm Password
                </label>
                <div className="relative">
                  <Lock className="absolute left-4 top-1/2 transform -translate-y-1/2 w-6 h-6 text-muted-foreground" strokeWidth={1.5} />
                  <input
                    type="password"
                    id="confirmPassword"
                    value={confirmPassword}
                    onChange={(e) => {
                      setConfirmPassword(e.target.value)
                      if (e.target.value && password && e.target.value !== password) {
                        setConfirmPasswordError('Passwords do not match')
                      } else {
                        setConfirmPasswordError('')
                      }
                    }}
                    className="auth-input pl-12 pr-4 py-4 min-h-[44px] rounded-lg focus:ring-2 focus:ring-primary text-lg"
                    placeholder="Confirm your password"
                    required={isSignUp}
                    aria-describedby={confirmPasswordError ? "confirm-password-error" : undefined}
                    aria-invalid={confirmPasswordError ? "true" : "false"}
                  />
                </div>
                {confirmPasswordError && (
                  <div id="confirm-password-error" className="mt-2 text-sm text-destructive" role="alert" aria-live="polite">
                    {confirmPasswordError}
                  </div>
                )}
              </div>
            )}

            {/* General Error Message */}
            {error && (
              <div id="auth-error" className="p-4 bg-muted border border-border rounded-lg" role="alert" aria-live="polite">
                <p className="text-sm text-foreground">{error}</p>
              </div>
            )}

            {/* Success Message */}
            {success && (
              <div className="p-4 bg-accent border border-border rounded-lg" role="alert" aria-live="polite">
                <p className="text-sm text-foreground">{success}</p>
              </div>
            )}

            {/* Forgot Password Link */}
            {!isSignUp && (
              <div className="text-right">
                <button
                  type="button"
                  onClick={handlePasswordReset}
                  disabled={loading}
                  className="text-primary hover:text-primary-hover font-medium transition-colors duration-300 text-sm min-h-[44px] px-2 py-2 flex items-center justify-end focus:outline-none focus:ring-2 focus:ring-primary rounded disabled:opacity-50"
                  aria-label="Reset forgotten password"
                >
                  Forgot your password?
                </button>
              </div>
            )}

            {/* Submit Button */}
            <button
              type="submit"
              disabled={loading}
              className="auth-button w-full px-8 py-4 min-h-[44px] rounded-lg disabled:opacity-50 transition-all duration-300 font-semibold text-lg shadow-lg hover:shadow-xl"
              aria-describedby={error ? "auth-error" : undefined}
            >
              {loading ? 'Please wait...' : (isSignUp ? 'Create Account' : 'Sign In')}
            </button>
          </form>

          {/* Toggle Auth Mode */}
          <div id="auth-toggle" className="mt-10 text-center">
            <p className="text-muted-foreground text-lg">
              {isSignUp ? 'Already have an account?' : "Don't have an account?"}
              <button
                onClick={() => setIsSignUp(!isSignUp)}
                className="ml-2 text-primary hover:text-primary-hover font-semibold transition-colors duration-300 min-h-[44px] py-2 px-1"
                aria-label={isSignUp ? 'Switch to sign in' : 'Switch to sign up'}
              >
                {isSignUp ? 'Sign In' : 'Sign Up'}
              </button>
            </p>

            {/* Forgot Password Link for Sign In */}
            {!isSignUp && (
              <div className="mt-4">
                <button
                  type="button"
                  className="text-sm text-muted-foreground hover:text-primary transition-colors min-h-[44px] py-2 px-1 disabled:opacity-50"
                  onClick={handlePasswordReset}
                  disabled={loading}
                >
                  Forgot your password?
                </button>
              </div>
            )}
          </div>
          </main>
        </div>
      </div>
      </main>
    </div>
  )
}
