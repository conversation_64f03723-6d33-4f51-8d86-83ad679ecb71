import { BrowserRouter, Routes, Route } from 'react-router-dom'
import Layout from './components/Layout'
import AppLayout from './components/AppLayout'
import LandingPage from './pages/LandingPage'
import AuthPage from './pages/AuthPage'
import ResetPasswordPage from './pages/ResetPasswordPage'
import DashboardPage from './pages/DashboardPage'
import ProgressPage from './pages/ProgressPage'
import GoalsPage from './pages/GoalsPage'
import BreathingPage from './pages/BreathingPage'
import FocusPage from './pages/FocusPage'
import MoodPage from './pages/MoodPage'
import CommunityPage from './pages/CommunityPage'
import LearnPage from './pages/LearnPage'
import SettingsPage from './pages/SettingsPage'
import HowItWorksPage from './pages/HowItWorksPage'
import FeaturesPage from './pages/FeaturesPage'
import ToolsPage from './pages/ToolsPage'
import NRTGuidePage from './pages/NRTGuidePage'
import SmokelessDirectoryPage from './pages/SmokelessDirectoryPage'
import QuitMethodsPage from './pages/QuitMethodsPage'
import NRTProductsPage from './pages/NRTProductsPage'
import CalculatorsPage from './pages/CalculatorsPage'
import HolisticHealthPage from './pages/HolisticHealthPage'
import FreshAssistantPage from './pages/FreshAssistantPage'
import AIAssistantPage from './pages/AIAssistantPage'
import AccountPage from './pages/AccountPage'
import LogEntryPage from './pages/LogEntryPage'
import RewardsPage from './pages/RewardsPage'
import HealthIntegrationsPage from './pages/HealthIntegrationsPage'
import JournalPage from './pages/JournalPage'
import SupportPage from './pages/SupportPage'
import BreathingToolsPage from './pages/BreathingToolsPage'
import SearchPage from './pages/SearchPage'
import AboutPage from './pages/AboutPage'
import PrivacyPolicyPage from './pages/PrivacyPolicyPage'
import TermsOfServicePage from './pages/TermsOfServicePage'
import HelpCenterPage from './pages/HelpCenterPage'
import ContactUsPage from './pages/ContactUsPage'
import FAQPage from './pages/FAQPage'
import FeedbackPage from './pages/FeedbackPage'
import { AuthProvider } from './contexts/AuthContext'

export default function Router() {
  return (
    <BrowserRouter>
      <AuthProvider>
        <Routes>
          {/* Standalone auth page without layout */}
          <Route path="auth" element={<AuthPage />} />
          <Route path="reset-password" element={<ResetPasswordPage />} />

          <Route path="/" element={<Layout />}>
            <Route index element={<LandingPage />} />
            <Route path="how-it-works" element={<HowItWorksPage />} />
            <Route path="features" element={<FeaturesPage />} />
            <Route path="tools" element={<ToolsPage />} />
            <Route path="tools/nrt-guide" element={<NRTGuidePage />} />
            <Route path="tools/nrt-products" element={<NRTProductsPage />} />
            <Route path="tools/smokeless-directory" element={<SmokelessDirectoryPage />} />
            <Route path="tools/quit-methods" element={<QuitMethodsPage />} />
            <Route path="quit-methods" element={<QuitMethodsPage />} />
            <Route path="tools/calculators" element={<CalculatorsPage />} />
            <Route path="tools/holistic-health" element={<HolisticHealthPage />} />
            <Route path="fresh-assistant" element={<FreshAssistantPage />} />
            <Route path="ai-assistant" element={<AIAssistantPage />} />
            <Route path="search" element={<SearchPage />} />
            <Route path="account" element={<AccountPage />} />
            <Route path="about" element={<AboutPage />} />
            <Route path="privacy-policy" element={<PrivacyPolicyPage />} />
            <Route path="terms-of-service" element={<TermsOfServicePage />} />
            <Route path="help-center" element={<HelpCenterPage />} />
            <Route path="contact-us" element={<ContactUsPage />} />
            <Route path="faq" element={<FAQPage />} />
            <Route path="feedback" element={<FeedbackPage />} />
          </Route>
          <Route path="dashboard" element={<AppLayout />}>
            <Route index element={<DashboardPage />} />
            <Route path="progress" element={<ProgressPage />} />
            <Route path="goals" element={<GoalsPage />} />
            <Route path="log" element={<LogEntryPage />} />
            <Route path="rewards" element={<RewardsPage />} />
            <Route path="breathing" element={<BreathingPage />} />
            <Route path="focus" element={<FocusPage />} />
            <Route path="mood" element={<MoodPage />} />
            <Route path="community" element={<CommunityPage />} />
            <Route path="learn" element={<LearnPage />} />
            <Route path="settings" element={<SettingsPage />} />
            <Route path="health-integrations" element={<HealthIntegrationsPage />} />
            <Route path="journal" element={<JournalPage />} />
            <Route path="support" element={<SupportPage />} />
          </Route>
        </Routes>
      </AuthProvider>
    </BrowserRouter>
  )
}
