-- Fix user_goals table schema to match application requirements
-- HOLY RULE 0001: Real database schema to replace mismatched fields

-- First, backup existing data if any exists
CREATE TABLE IF NOT EXISTS user_goals_backup AS 
SELECT * FROM user_goals;

-- Drop the existing table (if it has the old schema)
DROP TABLE IF EXISTS user_goals;

-- Create the new user_goals table with correct schema
CREATE TABLE user_goals (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  goal_type VARCHAR(50) NOT NULL CHECK (goal_type IN ('quit_nicotine', 'reduce_usage')),
  method VARCHAR(50) NOT NULL CHECK (method IN ('cold_turkey', 'gradual_reduction')),
  quit_date TIMESTAMP WITH TIME ZONE NOT NULL,
  motivation TEXT,
  typical_daily_usage DECIMAL(10,2) DEFAULT 0,
  cost_per_unit DECIMAL(10,2) DEFAULT 0,
  years_smoking INTEGER DEFAULT 0,
  replacement_method TEXT,
  support_system TEXT,
  status VARCHAR(20) DEFAULT 'in_progress' CHECK (status IN ('in_progress', 'completed', 'paused', 'cancelled')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX idx_user_goals_user_id ON user_goals(user_id);
CREATE INDEX idx_user_goals_status ON user_goals(status);
CREATE INDEX idx_user_goals_quit_date ON user_goals(quit_date);

-- Enable Row Level Security
ALTER TABLE user_goals ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
CREATE POLICY "Users can view their own goals" ON user_goals
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own goals" ON user_goals
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own goals" ON user_goals
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own goals" ON user_goals
  FOR DELETE USING (auth.uid() = user_id);

-- Grant permissions
GRANT ALL ON user_goals TO authenticated;
GRANT USAGE ON SCHEMA public TO authenticated;

-- Create trigger to automatically update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_user_goals_updated_at 
  BEFORE UPDATE ON user_goals 
  FOR EACH ROW 
  EXECUTE FUNCTION update_updated_at_column();

-- Migrate data from backup if it exists and has compatible fields
INSERT INTO user_goals (
  user_id, 
  goal_type, 
  method, 
  quit_date, 
  motivation, 
  typical_daily_usage, 
  years_smoking,
  status,
  created_at,
  updated_at
)
SELECT 
  user_id,
  'quit_nicotine' as goal_type, -- Default value for migrated data
  'cold_turkey' as method, -- Default value for migrated data
  COALESCE(target_quit_date, NOW()) as quit_date,
  motivation,
  COALESCE(cigarettes_per_day, 0) as typical_daily_usage,
  COALESCE(years_smoking, 0) as years_smoking,
  'in_progress' as status,
  COALESCE(created_at, NOW()) as created_at,
  COALESCE(updated_at, NOW()) as updated_at
FROM user_goals_backup
WHERE EXISTS (SELECT 1 FROM user_goals_backup);

-- Clean up backup table
DROP TABLE IF EXISTS user_goals_backup;

-- Verify the new schema
SELECT 
  column_name, 
  data_type, 
  is_nullable, 
  column_default
FROM information_schema.columns 
WHERE table_name = 'user_goals' 
ORDER BY ordinal_position;
